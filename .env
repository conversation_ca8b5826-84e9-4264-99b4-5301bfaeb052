EXPERIMENT_NAME=monolit-mf-ab-test
EXPERIMENT_GROUP_A=Monolit
EXPERIMENT_GROUP_B=MF
EXPERIMENT_EVENT_HIT=experiments.hit
# Base URL for the target service
BASE_URL=https://experience.staging.k8s-dev.tripster.ru
# Paths for different groups
GROUP_A_PATH=/experience/Saint_Petersburg/
GROUP_B_PATH=/cheetah/experience/Saint_Petersburg/
BROWSER_DEBUG_LOGS=false
# Unleash configuration
UNLEASH_URL=https://gitlab.tripster.ru/api/v4/feature_flags/unleash/5
UNLEASH_API_KEY=Wmpd3pK4ySze6anxTgxc
UNLEASH_APP_NAME=splitter
UNLEASH_FEATURE=ab-monolit-mf-experience
SOGU_URL=https://sogu-staging.sogu.dev.tripster.tech/events/
