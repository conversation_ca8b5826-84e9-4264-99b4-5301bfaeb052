package main

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"log/slog"
	"net/http"
	"net/http/httputil"
	"net/url"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/Unleash/unleash-client-go/v4"
	unleashContext "github.com/Unleash/unleash-client-go/v4/context"
	"github.com/google/uuid"
)

const (
	deviceCookie = "device_id"
)

// Config holds all application configuration
type Config struct {
	ExperimentName     string
	ExperimentGroupA   string
	ExperimentGroupB   string
	ExperimentEventHit string
	SoguURL            string
	ProxyURL           string
	RedirectBaseURL    string
	RedirectTarget     *url.URL
	UnleashURL         string
	UnleashAPIKey      string
	UnleashAppName     string
	UnleashFeature     string
	ServiceTarget      *url.URL
	AppVersion         string
	Port               string
	ShutdownTimeout    time.Duration
	HTTPTimeout        time.Duration
}

type App struct {
	config        *Config
	httpClient    *http.Client
	logger        *slog.Logger
	server        *http.Server
	unleashClient *unleash.Client
}

func main() {
	// Initialize structured logger
	logger := slog.New(slog.NewJSONHandler(os.Stdout, &slog.HandlerOptions{
		Level: slog.LevelDebug,
	}))

	// Load configuration
	config, err := loadConfig()
	if err != nil {
		logger.Error("Failed to load configuration", "error", err)
		os.Exit(1)
	}

	// Initialize Unleash client
	unleashClient, err := unleash.NewClient(
		unleash.WithUrl(config.UnleashURL),
		unleash.WithInstanceId(config.UnleashAPIKey),
		unleash.WithAppName(config.UnleashAppName),
		unleash.WithRefreshInterval(15*time.Second),
	)
	if err != nil {
		logger.Error("Failed to initialize Unleash client", "error", err)
		os.Exit(1)
	}

	// Create application instance
	app := &App{
		config: config,
		httpClient: &http.Client{
			Timeout: config.HTTPTimeout,
		},
		logger:        logger,
		unleashClient: unleashClient,
	}

	// Setup HTTP server with timeouts
	mux := http.NewServeMux()
	mux.HandleFunc("/", app.splitHandler)
	mux.HandleFunc("/health", app.healthHandler)

	app.server = &http.Server{
		Addr:         ":" + config.Port,
		Handler:      mux,
		ReadTimeout:  30 * time.Second,
		WriteTimeout: 30 * time.Second,
		IdleTimeout:  120 * time.Second,
	}

	// Log configuration
	logger.Info("Configuration loaded",
		"proxy_url", config.ProxyURL,
		"redirect_base_url", config.RedirectBaseURL,
		"unleash_feature", config.UnleashFeature)

	// Start server in goroutine
	go func() {
		logger.Info("Starting splitter server", "port", config.Port, "version", app.config.AppVersion)
		if err := app.server.ListenAndServe(); err != nil && !errors.Is(err, http.ErrServerClosed) {
			logger.Error("Server failed to start", "error", err)
			os.Exit(1)
		}
	}()

	// Wait for interrupt signal for graceful shutdown
	app.gracefulShutdown()
}

// loadConfig loads configuration from environment variables
func loadConfig() (*Config, error) {
	config := &Config{
		ExperimentName:     getenv("EXPERIMENT_NAME", "monolit-mf-ab-test"),
		ExperimentGroupA:   getenv("EXPERIMENT_GROUP_A", "Monolit"),
		ExperimentGroupB:   getenv("EXPERIMENT_GROUP_B", "MF"),
		ExperimentEventHit: getenv("EXPERIMENT_EVENT_HIT", "experiments.hit"),
		RedirectBaseURL:    getenv("REDIRECT_BASE_URL", "https://google.com"),
		ProxyURL:           getenv("PROXY_URL", "https://ya.ru"),
		SoguURL:            getenv("SOGU_URL", "https://sogu-staging.sogu.dev.tripster.tech/events/"),
		UnleashURL:         getenv("UNLEASH_URL", ""),
		UnleashAPIKey:      getenv("UNLEASH_API_KEY", ""),
		UnleashAppName:     getenv("UNLEASH_APP_NAME", "splitter"),
		UnleashFeature:     getenv("UNLEASH_FEATURE", "monolit-mf-ab-test"),
		Port:               getenv("PORT", "8080"),
		AppVersion:         getenv("APP_VERSION", "staging"),
		ShutdownTimeout:    30 * time.Second,
		HTTPTimeout:        10 * time.Second,
	}

	// Parse service target URL
	serviceTarget, err := url.Parse(config.ProxyURL)
	if err != nil {
		return nil, fmt.Errorf("invalid PROXY_URL: %v", err)
	}
	config.ServiceTarget = serviceTarget

	// Parse redirect target URL
	redirectTarget, err := url.Parse(config.RedirectBaseURL)
	if err != nil {
		return nil, fmt.Errorf("invalid REDIRECT_BASE_URL: %v", err)
	}
	config.RedirectTarget = redirectTarget

	return config, nil
}

// gracefulShutdown handles graceful shutdown of the application
func (app *App) gracefulShutdown() {
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)

	<-quit
	app.logger.Info("Shutting down server...")

	// Close Unleash client
	err := app.unleashClient.Close()
	if err != nil {
		app.logger.Error("Failed to close Unleash client", "error", err)
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), app.config.ShutdownTimeout)
	defer cancel()

	if err := app.server.Shutdown(ctx); err != nil {
		app.logger.Error("Server forced to shutdown", "error", err)
		os.Exit(1)
	}

	app.logger.Info("Server exited")
}

// splitHandler handles the main A/B testing logic
func (app *App) splitHandler(w http.ResponseWriter, r *http.Request) {
	deviceID := app.getOrSetDeviceID(w, r)

	// Create Unleash context with user ID
	unleashCtx := &unleashContext.Context{
		UserId: deviceID,
	}

	// Check if feature is enabled for this user
	//enabled := app.unleashClient.IsEnabled(app.config.UnleashFeature, unleash.WithContext(*unleashCtx))
	variant := app.unleashClient.GetVariant(app.config.UnleashFeature, unleash.WithVariantContext(*unleashCtx))

	// Determine group based on Unleash decision
	var group string
	if variant.FeatureEnabled {
		group = app.config.ExperimentGroupB
	} else {
		group = app.config.ExperimentGroupA
	}

	// Send analytics asynchronously
	go app.sendAnalytics(r, deviceID, group)

	app.logger.Info("Request processed",
		"device_id", deviceID,
		"group", group,
		"path", r.URL.Path)

	// Redirect group B to different URL
	if group == app.config.ExperimentGroupB {
		// Build redirect URL with the same path as the original request
		redirectURL := app.config.RedirectTarget.Scheme + "://" + app.config.RedirectTarget.Host
		if app.config.RedirectTarget.Path != "" {
			redirectURL += app.config.RedirectTarget.Path
		}
		redirectURL += r.URL.Path
		if r.URL.RawQuery != "" {
			redirectURL += "?" + r.URL.RawQuery
		}



		http.Redirect(w, r, redirectURL, http.StatusFound)
		return
	}

	// Proxy to the target service
	proxy := httputil.NewSingleHostReverseProxy(app.config.ServiceTarget)

	// Modify the request to preserve the original path
	originalDirector := proxy.Director
	proxy.Director = func(req *http.Request) {
		// Save the original request path BEFORE calling originalDirector
		originalPath := req.URL.Path

		// Call the original director to set up the basic proxy
		originalDirector(req)

		// Keep the original path as is (don't modify it)
		req.URL.Path = originalPath

		// Ensure proper headers are set
		req.Host = app.config.ServiceTarget.Host
		req.Header.Set("X-Forwarded-For", req.RemoteAddr)
		req.Header.Set("X-Forwarded-Proto", "https")
		req.Header.Set("X-Real-IP", req.RemoteAddr)


	}



	proxy.ErrorHandler = func(w http.ResponseWriter, r *http.Request, err error) {
		app.logger.Error("Proxy error",
			"error", err,
			"path", r.URL.Path,
			"method", r.Method)
		http.Error(w, "Service temporarily unavailable", http.StatusBadGateway)
	}
	proxy.ServeHTTP(w, r)
}

func (app *App) getCookie(r *http.Request, name string) string {
	cookie, err := r.Cookie(name)
	if err == nil && cookie.Value != "" {
		return cookie.Value
	}
	return ""
}

func (app *App) getOrSetDeviceID(w http.ResponseWriter, r *http.Request) string {
	cookieValue := app.getCookie(r, deviceCookie)
	if cookieValue != "" {
		return cookieValue
	}

	newID := uuid.New().String()
	http.SetCookie(w, &http.Cookie{
		Name:     deviceCookie,
		Value:    newID,
		Path:     "/",
		HttpOnly: true,
		Secure:   true,
		SameSite: http.SameSiteLaxMode,
		MaxAge:   30 * 24 * 3600, // 30 days
	})
	return newID
}

func (app *App) sendAnalytics(r *http.Request, deviceID, group string) {
	if app.config.SoguURL == "" {
		return
	}

	gaClientID := app.getCookie(r, "_ga")
	yandexClientID := app.getCookie(r, "_ym_uid")

	type Params struct {
		DeviceID     string `json:"device_id"`
		Experiment   string `json:"experiment"`
		Variant      string `json:"variant"`
		WasGenerated bool   `json:"was_generated"`
	}

	type Event struct {
		AppVersion string `json:"app_version"`
		EventName  string `json:"event_name"`
		Platform   string `json:"platform"`
		URL        string `json:"url"`
		GAClientID string `json:"ga_client_id"`
		YAClientID string `json:"ya_client_id"`
		UserAgent  string `json:"user_agent"`
		DeviceID   string `json:"device_id"`
		DT         int64  `json:"dt"`
		Params     Params `json:"params"`
	}

	event := Event{
		AppVersion: app.config.AppVersion,
		EventName:  app.config.ExperimentEventHit,
		Platform:   "web",
		URL:        r.URL.String(),
		GAClientID: gaClientID,
		YAClientID: yandexClientID,
		UserAgent:  r.Header.Get("User-Agent"),
		DeviceID:   deviceID,
		DT:         time.Now().Unix(),
		Params: Params{
			DeviceID:     deviceID,
			Experiment:   app.config.ExperimentName,
			Variant:      group,
			WasGenerated: false,
		},
	}

	_, err := json.Marshal([]Event{event})
	if err != nil {
		app.logger.Error("Failed to marshal analytics payload", "error", err)
		return
	}



	// Асинхронная отправка с использованием HTTP клиента с таймаутом
	//go func() {
	//	resp, err := app.httpClient.Post(app.config.SoguURL, "application/json", bytes.NewBuffer(body))
	//	if err != nil {
	//		app.logger.Error("Failed to send analytics", "error", err)
	//		return
	//	}
	//	defer resp.Body.Close()
	//
	//	if resp.StatusCode >= 400 {
	//		app.logger.Error("Analytics request failed", "status_code", resp.StatusCode)
	//	} else {
	//		app.logger.Debug("Analytics sent successfully", "device_id", deviceID, "group", group)
	//	}
	//}()
}

// Health check handlers
func (app *App) healthHandler(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)

	response := map[string]interface{}{
		"status":  "healthy",
		"version": app.config.AppVersion,
		"time":    time.Now().UTC().Format(time.RFC3339),
	}

	err := json.NewEncoder(w).Encode(response)
	if err != nil {
		app.logger.Error("Failed to encode health response", "error", err)
		return
	}
}

func getenv(key, fallback string) string {
	val := os.Getenv(key)
	if val == "" {
		return fallback
	}
	return val
}
